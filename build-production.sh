#!/bin/bash

echo "Building for production deployment..."

# Copy production environment file
cp .env.production .env

# Clear Laravel caches
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

# Install dependencies
npm install

# Build assets for production
npm run production

# Optimize <PERSON>vel for production
php artisan config:cache
php artisan route:cache
php artisan view:cache

echo "Production build completed!"
echo ""
echo "Next steps:"
echo "1. Upload the entire project to your server"
echo "2. Make sure the web server points to the 'public' directory"
echo "3. Ensure the .htaccess file is properly configured"
echo "4. Set proper file permissions (755 for directories, 644 for files)"
echo "5. Make sure storage and bootstrap/cache directories are writable"
echo ""
