const mix = require('laravel-mix');

// mix.options({
//     resourceRoot: process.env.VUE_PUBLIC_PATH,
// });

// mix.webpackConfig({
//   plugins: [
//     new webpack.DefinePlugin({
//       'process.env.VUE_ROUTER_BASE': JSON.stringify(process.env.VUE_ROUTER_BASE || '/'),
//     }),
//   ]
// });


mix.js('resources/js/app.js', 'public/js')
    .vue()
    .postCss('resources/css/app.css', 'public/css', [
        require('tailwindcss'),
        require('autoprefixer'),
    ])
    .version();
